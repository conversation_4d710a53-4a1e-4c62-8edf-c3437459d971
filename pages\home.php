<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-bg">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title"><?php echo $translations['hero_title']; ?></h1>
                        <h2 class="hero-subtitle"><?php echo $translations['hero_subtitle']; ?></h2>
                        <p class="hero-description"><?php echo $translations['hero_description']; ?></p>
                        
                        <div class="hero-buttons">
                            <a href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>" class="btn btn-primary btn-lg me-3">
                                <?php echo $translations['hero_cta_primary']; ?>
                            </a>
                            <a href="<?php echo BASE_URL; ?>/portfolio?lang=<?php echo $lang; ?>" class="btn btn-outline-primary btn-lg">
                                <?php echo $translations['hero_cta_secondary']; ?>
                            </a>
                        </div>
                        
                        <!-- Stats -->
                        <div class="hero-stats mt-5">
                            <div class="row">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h3 class="stat-number">5+</h3>
                                        <p class="stat-label"><?php echo $translations['about_experience']; ?></p>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h3 class="stat-number">100+</h3>
                                        <p class="stat-label"><?php echo $translations['about_projects']; ?></p>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h3 class="stat-number">50+</h3>
                                        <p class="stat-label"><?php echo $translations['about_clients']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <img src="assets/images/hero-image.png" alt="Web Development" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title"><?php echo $translations['services_title']; ?></h2>
                <p class="section-subtitle"><?php echo $translations['services_subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php
            $services = getServices($lang);
            $serviceIcons = [
                'web-design' => 'fas fa-paint-brush',
                'web-development' => 'fas fa-code',
                'ecommerce' => 'fas fa-shopping-cart',
                'seo' => 'fas fa-search',
                'maintenance' => 'fas fa-tools',
                'hosting' => 'fas fa-server'
            ];
            
            $defaultServices = [
                [
                    'title' => $translations['service_web_design'],
                    'description' => 'Beautiful, responsive designs that captivate your audience',
                    'icon' => 'fas fa-paint-brush'
                ],
                [
                    'title' => $translations['service_web_development'],
                    'description' => 'Custom web applications built with latest technologies',
                    'icon' => 'fas fa-code'
                ],
                [
                    'title' => $translations['service_ecommerce'],
                    'description' => 'Complete e-commerce solutions for online businesses',
                    'icon' => 'fas fa-shopping-cart'
                ],
                [
                    'title' => $translations['service_seo'],
                    'description' => 'Improve your search engine rankings and visibility',
                    'icon' => 'fas fa-search'
                ],
                [
                    'title' => $translations['service_maintenance'],
                    'description' => 'Keep your website updated and running smoothly',
                    'icon' => 'fas fa-tools'
                ],
                [
                    'title' => $translations['service_hosting'],
                    'description' => 'Reliable and secure web hosting solutions',
                    'icon' => 'fas fa-server'
                ]
            ];
            
            $displayServices = !empty($services) ? $services : $defaultServices;
            
            foreach ($displayServices as $service):
            ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="<?php echo $service['icon'] ?? 'fas fa-cog'; ?>"></i>
                    </div>
                    <h4 class="service-title"><?php echo $service['title']; ?></h4>
                    <p class="service-description"><?php echo $service['description']; ?></p>
                    <a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>" class="service-link">
                        <?php echo $translations['learn_more']; ?> <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="about-image">
                    <img src="assets/images/about-us.jpg" alt="About Us" class="img-fluid rounded">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content">
                    <h2 class="section-title"><?php echo $translations['about_title']; ?></h2>
                    <h3 class="section-subtitle"><?php echo $translations['about_subtitle']; ?></h3>
                    <p class="mb-4"><?php echo $translations['about_description']; ?></p>
                    
                    <div class="about-features">
                        <div class="feature-item mb-3">
                            <i class="fas fa-check-circle text-primary me-2"></i>
                            <span>Professional Team of Developers</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-check-circle text-primary me-2"></i>
                            <span>Latest Technologies & Best Practices</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-check-circle text-primary me-2"></i>
                            <span>24/7 Customer Support</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-check-circle text-primary me-2"></i>
                            <span>Affordable Pricing Plans</span>
                        </div>
                    </div>
                    
                    <a href="<?php echo BASE_URL; ?>/about?lang=<?php echo $lang; ?>" class="btn btn-primary">
                        <?php echo $translations['learn_more']; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title"><?php echo $translations['features_title']; ?></h2>
                <p class="section-subtitle"><?php echo $translations['features_subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="feature-title"><?php echo $translations['feature_responsive']; ?></h4>
                    <p class="feature-description"><?php echo $translations['feature_responsive_desc']; ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4 class="feature-title"><?php echo $translations['feature_fast']; ?></h4>
                    <p class="feature-description"><?php echo $translations['feature_fast_desc']; ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h4 class="feature-title"><?php echo $translations['feature_seo']; ?></h4>
                    <p class="feature-description"><?php echo $translations['feature_seo_desc']; ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title"><?php echo $translations['feature_secure']; ?></h4>
                    <p class="feature-description"><?php echo $translations['feature_secure_desc']; ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="cta-title">Ready to Start Your Project?</h2>
                <p class="cta-description">Get a free consultation and quote for your web development project today.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>" class="btn btn-light btn-lg">
                    <?php echo $translations['get_quote']; ?>
                </a>
            </div>
        </div>
    </div>
</section>
