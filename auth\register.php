<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        redirect('../admin/dashboard.php');
    } else {
        redirect('../client/dashboard.php');
    }
}

// Get current language
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
if (!in_array($lang, ['en', 'hi'])) {
    $lang = 'en';
}

// Load language file
$translations = loadLanguage($lang);

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($username) || empty($email) || empty($full_name) || empty($password) || empty($confirm_password)) {
        $error = 'Please fill in all fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } else {
        try {
            // Check if username or email already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            
            if ($stmt->fetchColumn() > 0) {
                $error = 'Username or email already exists.';
            } else {
                // Create new user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("INSERT INTO users (username, email, full_name, password, role, status) VALUES (?, ?, ?, ?, 'client', 'active')");
                
                if ($stmt->execute([$username, $email, $full_name, $hashedPassword])) {
                    $success = $translations['auth_register_success'];
                    
                    // Log activity
                    $userId = $pdo->lastInsertId();
                    logActivity($userId, 'register', 'User registered');
                    
                    // Send welcome email (optional)
                    $subject = "Welcome to " . SITE_NAME;
                    $message = "
                        <h2>Welcome to " . SITE_NAME . "!</h2>
                        <p>Thank you for registering with us. Your account has been created successfully.</p>
                        <p><strong>Username:</strong> $username</p>
                        <p><strong>Email:</strong> $email</p>
                        <p>You can now login to your account and start using our services.</p>
                        <p>Best regards,<br>" . SITE_NAME . " Team</p>
                    ";
                    
                    sendEmail($email, $subject, $message);
                    
                    // Clear form data
                    $username = $email = $full_name = '';
                } else {
                    $error = $translations['auth_register_error'];
                }
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translations['auth_register']; ?> - <?php echo $translations['site_title']; ?></title>
    <meta name="description" content="Create your account">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card">
                        <!-- Logo -->
                        <div class="auth-header text-center mb-4">
                            <a href="../?lang=<?php echo $lang; ?>">
                                <img src="../assets/images/logo.png" alt="Logo" height="60">
                            </a>
                            <h2 class="auth-title mt-3"><?php echo $translations['auth_register']; ?></h2>
                            <p class="auth-subtitle">Create your account to get started.</p>
                        </div>
                        
                        <!-- Language Switcher -->
                        <div class="language-switcher text-center mb-4">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="switchLanguage('en')" 
                                    <?php echo $lang === 'en' ? 'disabled' : ''; ?>>
                                English
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="switchLanguage('hi')" 
                                    <?php echo $lang === 'hi' ? 'disabled' : ''; ?>>
                                हिंदी
                            </button>
                        </div>
                        
                        <!-- Error/Success Messages -->
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Registration Form -->
                        <form method="POST" class="auth-form">
                            <div class="form-group mb-3">
                                <label for="full_name" class="form-label"><?php echo $translations['auth_full_name']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="username" class="form-label"><?php echo $translations['auth_username']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-at"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                </div>
                                <small class="form-text text-muted">Username must be unique and contain only letters, numbers, and underscores.</small>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="email" class="form-label"><?php echo $translations['auth_email']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="password" class="form-label"><?php echo $translations['auth_password']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="confirm_password" class="form-label"><?php echo $translations['auth_confirm_password']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="confirmPasswordToggle"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="../terms-of-service?lang=<?php echo $lang; ?>" target="_blank">Terms of Service</a> 
                                        and <a href="../privacy-policy?lang=<?php echo $lang; ?>" target="_blank">Privacy Policy</a>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-user-plus"></i> <?php echo $translations['auth_register']; ?>
                            </button>
                        </form>
                        
                        <!-- Additional Links -->
                        <div class="auth-links text-center">
                            <p class="mb-0">
                                <?php echo $translations['auth_have_account']; ?> 
                                <a href="login.php?lang=<?php echo $lang; ?>" class="text-primary">
                                    <?php echo $translations['auth_login']; ?>
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = fieldId === 'password' ? 
                document.getElementById('passwordToggle') : 
                document.getElementById('confirmPasswordToggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        function switchLanguage(lang) {
            const url = new URL(window.location);
            url.searchParams.set('lang', lang);
            window.location.href = url.toString();
        }
        
        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = getPasswordStrength(password);
            // You can add visual feedback here
        });
        
        function getPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            return strength;
        }
        
        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('full_name').focus();
        });
    </script>
</body>
</html>
