/* Responsive CSS for WebsiteDeveloper0002 */

/* Extra Large Devices (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
}

/* Large Devices (992px and up) */
@media (min-width: 992px) {
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
    }
    
    .hero-content {
        padding-right: 2rem;
    }
    
    .service-card {
        height: 100%;
    }
}

/* Medium Devices (768px and up) */
@media (min-width: 768px) {
    .top-bar {
        display: block !important;
    }
    
    .hero-buttons .btn {
        display: inline-block;
        width: auto;
        margin-bottom: 0;
        margin-right: 1rem;
    }
    
    .hero-buttons .btn:last-child {
        margin-right: 0;
    }
    
    .stat-item {
        border-right: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .stat-item:last-child {
        border-right: none;
    }
}

/* Small Devices (576px and up) */
@media (min-width: 576px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
    
    .service-card {
        margin-bottom: 2rem;
    }
}

/* Extra Small Devices (less than 576px) */
@media (max-width: 575.98px) {
    /* Typography */
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* Header */
    .top-bar {
        display: none !important;
    }
    
    .navbar {
        padding: 0.5rem 0;
    }
    
    .navbar-brand img {
        max-height: 40px;
    }
    
    .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
    }
    
    /* Hero Section */
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-content {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-buttons {
        margin-bottom: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .hero-buttons .btn:last-child {
        margin-bottom: 0;
    }
    
    .hero-stats .row {
        text-align: center;
    }
    
    .hero-stats .col-4 {
        margin-bottom: 1rem;
    }
    
    /* Services */
    .service-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        line-height: 60px;
        font-size: 1.5rem;
    }
    
    .service-title {
        font-size: 1.125rem;
    }
    
    /* Features */
    .feature-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 1.25rem;
    }
    
    /* About */
    .about-image {
        margin-bottom: 2rem;
    }
    
    .about-highlights .col-6 {
        margin-bottom: 1rem;
    }
    
    /* Stats */
    .stats-section .col-lg-3 {
        margin-bottom: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    /* Team */
    .team-member {
        margin-bottom: 2rem;
    }
    
    /* Pricing */
    .pricing-card {
        margin-bottom: 2rem;
    }
    
    .pricing-toggle {
        margin-bottom: 2rem;
    }
    
    /* Contact */
    .contact-form-wrapper {
        margin-bottom: 3rem;
    }
    
    .contact-info-item {
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .contact-info-item .info-icon {
        margin-bottom: 1rem;
    }
    
    /* Footer */
    .footer .col-lg-4,
    .footer .col-lg-2,
    .footer .col-lg-3 {
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .footer-links-inline {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .footer-links-inline li {
        margin-bottom: 0.5rem;
    }
    
    /* Forms */
    .form-group {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .btn-lg {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
    
    /* Cards */
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Modals */
    .modal-dialog {
        margin: 1rem;
    }
    
    /* Tables */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    /* Breadcrumb */
    .breadcrumb {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }
    
    /* Page Header */
    .page-header {
        padding: 2rem 0;
    }
    
    .page-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    /* CTA Section */
    .cta-section .col-lg-8,
    .cta-section .col-lg-4 {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .cta-title {
        font-size: 1.5rem;
    }
    
    .cta-description {
        font-size: 1rem;
    }
    
    /* Process Steps */
    .process-step {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .step-number {
        font-size: 1.5rem;
        width: 40px;
        height: 40px;
        line-height: 40px;
    }
    
    /* Technology Items */
    .tech-item {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    
    .tech-logo {
        max-width: 60px;
        max-height: 60px;
    }
    
    /* Values */
    .value-item {
        margin-bottom: 2rem;
    }
    
    .value-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    /* Add-ons */
    .addon-card {
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    /* FAQ */
    .accordion-button {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
    }
    
    .accordion-body {
        font-size: 0.875rem;
        padding: 1rem;
    }
    
    /* Back to Top */
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
    
    /* WhatsApp Float */
    .whatsapp-float {
        bottom: 80px;
        right: 20px;
    }
    
    .whatsapp-btn {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 1.25rem;
    }
    
    /* Cookie Consent */
    .cookie-consent {
        padding: 0.75rem 0;
    }
    
    .cookie-consent .col-md-8,
    .cookie-consent .col-md-4 {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .cookie-consent p {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }
    
    .cookie-consent .btn {
        margin: 0.25rem;
    }
}

/* Landscape Orientation */
@media (max-width: 767.98px) and (orientation: landscape) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-stats {
        margin-top: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .back-to-top,
    .whatsapp-float,
    .cookie-consent,
    .btn,
    .alert {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
    
    .page-header {
        background: none !important;
        color: #000 !important;
    }
    
    .hero-section {
        background: none !important;
        color: #000 !important;
    }
    
    .bg-primary,
    .bg-dark {
        background: none !important;
        color: #000 !important;
    }
    
    .text-white {
        color: #000 !important;
    }
    
    a {
        color: #000 !important;
        text-decoration: underline !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-outline-primary {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .nav-link:hover,
    .nav-link:focus {
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-title,
    .hero-subtitle,
    .hero-description,
    .hero-buttons,
    .hero-stats,
    .hero-image {
        animation: none !important;
    }
}
