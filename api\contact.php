<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get current language
$lang = $_POST['lang'] ?? 'en';
$translations = loadLanguage($lang);

try {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        throw new Exception('Invalid security token');
    }
    
    // Sanitize and validate input
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $budget = sanitizeInput($_POST['budget'] ?? '');
    $timeline = sanitizeInput($_POST['timeline'] ?? '');
    $newsletter = isset($_POST['newsletter']) ? 1 : 0;
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Name is required';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    // Check for spam (simple honeypot and rate limiting)
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Rate limiting: max 3 submissions per IP per hour
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contact_messages WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->execute([$ip]);
    $recentSubmissions = $stmt->fetchColumn();
    
    if ($recentSubmissions >= 3) {
        echo json_encode(['success' => false, 'message' => 'Too many submissions. Please try again later.']);
        exit;
    }
    
    // Insert message into database
    $stmt = $pdo->prepare("INSERT INTO contact_messages (name, email, phone, subject, message, budget, timeline, newsletter, ip_address, user_agent, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'new')");
    
    $result = $stmt->execute([
        $name,
        $email,
        $phone,
        $subject,
        $message,
        $budget,
        $timeline,
        $newsletter,
        $ip,
        $userAgent
    ]);
    
    if ($result) {
        $messageId = $pdo->lastInsertId();
        
        // Send notification email to admin
        $adminSubject = "New Contact Form Submission - " . SITE_NAME;
        $adminMessage = "
            <h2>New Contact Form Submission</h2>
            <p><strong>Name:</strong> $name</p>
            <p><strong>Email:</strong> $email</p>
            <p><strong>Phone:</strong> $phone</p>
            <p><strong>Subject:</strong> $subject</p>
            <p><strong>Budget:</strong> $budget</p>
            <p><strong>Timeline:</strong> $timeline</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br($message) . "</p>
            <p><strong>IP Address:</strong> $ip</p>
            <p><strong>Submitted:</strong> " . date('Y-m-d H:i:s') . "</p>
            <hr>
            <p><a href='" . BASE_URL . "/admin/messages.php?id=$messageId'>View in Admin Panel</a></p>
        ";
        
        sendEmail(ADMIN_EMAIL, $adminSubject, $adminMessage);
        
        // Send auto-reply to user
        $userSubject = "Thank you for contacting " . SITE_NAME;
        $userMessage = "
            <h2>Thank you for your message!</h2>
            <p>Dear $name,</p>
            <p>Thank you for contacting us. We have received your message and will get back to you within 24 hours.</p>
            <p><strong>Your message details:</strong></p>
            <p><strong>Subject:</strong> $subject</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br($message) . "</p>
            <hr>
            <p>Best regards,<br>" . SITE_NAME . " Team</p>
            <p><strong>Contact Information:</strong><br>
            Email: " . ADMIN_EMAIL . "<br>
            Phone: +91 9876543210</p>
        ";
        
        sendEmail($email, $userSubject, $userMessage);
        
        // Subscribe to newsletter if requested
        if ($newsletter) {
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO newsletter_subscribers (email, name, status, subscribed_at) VALUES (?, ?, 'active', NOW())");
                $stmt->execute([$email, $name]);
            } catch (PDOException $e) {
                // Newsletter subscription failed, but don't fail the whole process
                error_log("Newsletter subscription failed: " . $e->getMessage());
            }
        }
        
        // Log activity
        logActivity(0, 'contact_form', "Contact form submitted by $name ($email)");
        
        echo json_encode([
            'success' => true, 
            'message' => $translations['message_contact_sent']
        ]);
        
    } else {
        throw new Exception('Failed to save message');
    }
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => $translations['message_contact_error']
    ]);
}
?>
