/* Custom CSS for WebsiteDeveloper0002 */

/* Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white-color: #ffffff;
    --black-color: #000000;
    
    --font-family-primary: 'Poppins', sans-serif;
    --font-size-base: 16px;
    --line-height-base: 1.6;
    
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--dark-color);
    background-color: var(--white-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Header Styles */
.top-bar {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.contact-info span {
    margin-right: 1rem;
}

.contact-info i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.language-switcher {
    display: inline-block;
    margin-right: 1rem;
}

.language-switcher .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
}

.social-links {
    display: inline-block;
}

.social-link {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white-color);
    margin-left: 0.5rem;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--white-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.navbar {
    padding: 1rem 0;
    box-shadow: var(--box-shadow);
}

.navbar-brand img {
    max-height: 50px;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white-color);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease 0.6s both;
}

.hero-stats {
    animation: fadeInUp 1s ease 0.8s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.hero-image {
    animation: fadeInRight 1s ease 0.5s both;
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
}

/* Services Section */
.service-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: var(--white-color);
    border-radius: 50%;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.service-description {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.service-link {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
}

.service-link:hover {
    color: #0056b3;
}

/* Features Section */
.feature-card {
    padding: 2rem 1rem;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    background: var(--light-color);
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.feature-description {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* About Section */
.about-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.feature-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.cta-description {
    font-size: 1.125rem;
    margin-bottom: 0;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white-color);
}

.footer-widget h5,
.footer-widget h6 {
    color: var(--white-color);
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #adb5bd;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white-color);
}

.footer-links-inline {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    color: var(--primary-color);
    margin-top: 0.25rem;
    width: 20px;
}

/* Newsletter */
.newsletter-form .input-group {
    max-width: 400px;
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 1000;
    display: none;
}

.back-to-top:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* WhatsApp Float */
.whatsapp-float {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-btn {
    display: block;
    width: 60px;
    height: 60px;
    background: #25d366;
    color: var(--white-color);
    border-radius: 50%;
    text-align: center;
    line-height: 60px;
    font-size: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: scale(1.1);
    color: var(--white-color);
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--dark-color);
    color: var(--white-color);
    padding: 1rem 0;
    z-index: 1050;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .top-bar {
        display: none;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .hero-buttons .btn:last-child {
        margin-bottom: 0;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
