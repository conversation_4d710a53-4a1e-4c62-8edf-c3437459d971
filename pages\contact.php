<!-- Contact Page -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title"><?php echo $translations['contact_title']; ?></h1>
                <p class="page-subtitle"><?php echo $translations['contact_subtitle']; ?></p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL; ?>?lang=<?php echo $lang; ?>" class="text-white">
                                <?php echo $translations['nav_home']; ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white"><?php echo $translations['nav_contact']; ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title"><?php echo $translations['contact_description']; ?></h2>
            </div>
        </div>
        
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="contact-form-wrapper">
                    <h3 class="form-title">Send us a Message</h3>
                    
                    <form id="contactForm" method="POST" action="api/contact.php" class="contact-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="lang" value="<?php echo $lang; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label"><?php echo $translations['contact_name']; ?> *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo $translations['contact_email']; ?> *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?php echo $translations['contact_phone']; ?></label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label"><?php echo $translations['contact_subject']; ?></label>
                                <select class="form-control" id="subject" name="subject">
                                    <option value="">Select a subject</option>
                                    <option value="Web Development">Web Development</option>
                                    <option value="Web Design">Web Design</option>
                                    <option value="E-commerce">E-commerce</option>
                                    <option value="SEO Services">SEO Services</option>
                                    <option value="Maintenance">Website Maintenance</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label"><?php echo $translations['contact_message']; ?> *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required 
                                      placeholder="Tell us about your project requirements..."></textarea>
                        </div>
                        
                        <!-- Budget Range -->
                        <div class="mb-3">
                            <label for="budget" class="form-label">Budget Range (Optional)</label>
                            <select class="form-control" id="budget" name="budget">
                                <option value="">Select budget range</option>
                                <option value="Under ₹25,000">Under ₹25,000</option>
                                <option value="₹25,000 - ₹50,000">₹25,000 - ₹50,000</option>
                                <option value="₹50,000 - ₹1,00,000">₹50,000 - ₹1,00,000</option>
                                <option value="₹1,00,000 - ₹2,00,000">₹1,00,000 - ₹2,00,000</option>
                                <option value="Above ₹2,00,000">Above ₹2,00,000</option>
                            </select>
                        </div>
                        
                        <!-- Timeline -->
                        <div class="mb-3">
                            <label for="timeline" class="form-label">Project Timeline (Optional)</label>
                            <select class="form-control" id="timeline" name="timeline">
                                <option value="">Select timeline</option>
                                <option value="ASAP">ASAP</option>
                                <option value="Within 1 month">Within 1 month</option>
                                <option value="1-3 months">1-3 months</option>
                                <option value="3-6 months">3-6 months</option>
                                <option value="6+ months">6+ months</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter" value="1">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for updates and tips
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> <?php echo $translations['contact_send']; ?>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info-wrapper">
                    <h3 class="info-title">Get in Touch</h3>
                    
                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <h5><?php echo $translations['contact_address']; ?></h5>
                            <p>123 Business Street<br>New Delhi, India 110001</p>
                        </div>
                    </div>
                    
                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <h5><?php echo $translations['contact_phone_label']; ?></h5>
                            <p><a href="tel:+************">+91 9876543210</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <h5><?php echo $translations['contact_email_label']; ?></h5>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <h5>Business Hours</h5>
                            <p>Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM<br>Sunday: Closed</p>
                        </div>
                    </div>
                    
                    <!-- Social Links -->
                    <div class="contact-social">
                        <h5>Follow Us</h5>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container-fluid p-0">
        <div class="map-wrapper">
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3501.************!2d77.20902731508236!3d28.65195908240251!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390cfd0683d1e6c7%3A0x1b8b1b8b1b8b1b8b!2sNew%20Delhi%2C%20Delhi!5e0!3m2!1sen!2sin!4v1635000000000!5m2!1sen!2sin" 
                    width="100%" height="400" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Frequently Asked Questions</h2>
                <p class="section-subtitle">Find answers to common questions about our services</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                How long does it take to build a website?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                The timeline depends on the complexity of your project. A simple website typically takes 2-4 weeks, while complex e-commerce or custom applications may take 6-12 weeks.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Do you provide ongoing maintenance?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, we offer comprehensive maintenance packages including regular updates, security monitoring, backups, and technical support.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                Will my website be mobile-friendly?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Absolutely! All our websites are built with a mobile-first approach and are fully responsive across all devices and screen sizes.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                Do you provide SEO services?
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, we offer comprehensive SEO services including keyword research, on-page optimization, technical SEO, and ongoing SEO maintenance.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    submitBtn.disabled = true;
    
    // Prepare form data
    const formData = new FormData(form);
    
    // Send form data
    fetch('api/contact.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showAlert('success', data.message || '<?php echo $translations['message_contact_sent']; ?>');
            form.reset();
        } else {
            // Show error message
            showAlert('error', data.message || '<?php echo $translations['message_contact_error']; ?>');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '<?php echo $translations['message_contact_error']; ?>');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const form = document.getElementById('contactForm');
    form.parentNode.insertBefore(alertDiv, form);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Pre-fill service if coming from services page
const urlParams = new URLSearchParams(window.location.search);
const service = urlParams.get('service');
if (service) {
    document.getElementById('subject').value = service;
}
</script>
