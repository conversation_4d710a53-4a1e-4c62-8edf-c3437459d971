<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Log activity before destroying session
if (isLoggedIn()) {
    logActivity($_SESSION['user_id'], 'logout', 'User logged out');
}

// Clear remember me cookie
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Remove token from database
    if (isLoggedIn()) {
        try {
            $stmt = $pdo->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
        } catch (PDOException $e) {
            // Handle error silently
        }
    }
}

// Destroy session
session_destroy();

// Get language for redirect
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';

// Redirect to home page
redirect('../?lang=' . $lang);
?>
