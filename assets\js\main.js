// Main JavaScript file for WebsiteDeveloper0002

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initializeComponents();
        setupEventListeners();
        handlePageLoad();
    });

    // Initialize all components
    function initializeComponents() {
        initBackToTop();
        initSmoothScroll();
        initLazyLoading();
        initTooltips();
        initCounters();
        initAOS();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Mobile menu toggle
        $('.navbar-toggler').on('click', function() {
            $(this).toggleClass('active');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.navbar').length) {
                $('.navbar-collapse').removeClass('show');
                $('.navbar-toggler').removeClass('active');
            }
        });

        // Form submissions
        setupFormHandlers();
        
        // Search functionality
        setupSearch();
        
        // Language switcher
        setupLanguageSwitcher();
    }

    // Back to top button
    function initBackToTop() {
        const backToTop = $('#backToTop');
        
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTop.fadeIn();
            } else {
                backToTop.fadeOut();
            }
        });

        backToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    // Smooth scrolling for anchor links
    function initSmoothScroll() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                let target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 80
                    }, 1000);
                    return false;
                }
            }
        });
    }

    // Lazy loading for images
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Initialize tooltips
    function initTooltips() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Counter animation
    function initCounters() {
        const counters = document.querySelectorAll('.counter');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            observer.observe(counter);
        });
    }

    function animateCounter(counter) {
        const target = parseInt(counter.getAttribute('data-count'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 20);
    }

    // Initialize AOS (Animate On Scroll)
    function initAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 1000,
                once: true,
                offset: 100
            });
        }
    }

    // Form handlers
    function setupFormHandlers() {
        // Contact form
        $('#contactForm').on('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this, 'api/contact.php');
        });

        // Newsletter form
        $('#newsletterForm').on('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this, 'api/newsletter.php');
        });

        // Generic form validation
        $('form[data-validate]').each(function() {
            setupFormValidation(this);
        });
    }

    function handleFormSubmission(form, url) {
        const $form = $(form);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.html();

        // Show loading state
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Sending...').prop('disabled', true);

        // Prepare form data
        const formData = new FormData(form);

        // Send AJAX request
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    form.reset();
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'An error occurred. Please try again.');
            },
            complete: function() {
                $submitBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    function setupFormValidation(form) {
        const $form = $(form);
        
        $form.find('input, textarea, select').on('blur', function() {
            validateField(this);
        });

        $form.on('submit', function(e) {
            let isValid = true;
            
            $form.find('input[required], textarea[required], select[required]').each(function() {
                if (!validateField(this)) {
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                showAlert('error', 'Please fill in all required fields correctly.');
            }
        });
    }

    function validateField(field) {
        const $field = $(field);
        const value = $field.val().trim();
        const type = $field.attr('type');
        let isValid = true;
        let message = '';

        // Remove existing validation classes
        $field.removeClass('is-valid is-invalid');
        $field.siblings('.invalid-feedback').remove();

        // Required field validation
        if ($field.prop('required') && !value) {
            isValid = false;
            message = 'This field is required.';
        }

        // Email validation
        if (type === 'email' && value && !isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address.';
        }

        // Phone validation
        if (type === 'tel' && value && !isValidPhone(value)) {
            isValid = false;
            message = 'Please enter a valid phone number.';
        }

        // Password validation
        if (type === 'password' && value && value.length < 6) {
            isValid = false;
            message = 'Password must be at least 6 characters long.';
        }

        // Add validation classes and feedback
        if (isValid) {
            $field.addClass('is-valid');
        } else {
            $field.addClass('is-invalid');
            $field.after('<div class="invalid-feedback">' + message + '</div>');
        }

        return isValid;
    }

    // Search functionality
    function setupSearch() {
        const searchInput = $('#globalSearch');
        const searchResults = $('#searchResults');
        let searchTimeout;

        if (searchInput.length) {
            searchInput.on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val().trim();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performSearch(query);
                    }, 300);
                } else {
                    searchResults.empty();
                }
            });
        }
    }

    function performSearch(query) {
        const searchResults = $('#searchResults');
        
        searchResults.html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Searching...</div>');

        $.ajax({
            url: 'api/search.php',
            type: 'POST',
            data: { query: query },
            dataType: 'json',
            success: function(results) {
                displaySearchResults(results);
            },
            error: function() {
                searchResults.html('<div class="text-center py-3 text-danger">Search failed. Please try again.</div>');
            }
        });
    }

    function displaySearchResults(results) {
        const searchResults = $('#searchResults');
        
        if (results.length === 0) {
            searchResults.html('<div class="text-center py-3 text-muted">No results found.</div>');
            return;
        }

        let html = '<div class="list-group">';
        results.forEach(result => {
            html += `
                <a href="${result.url}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${result.title}</h6>
                        <small class="text-muted">${result.type}</small>
                    </div>
                    <p class="mb-1">${result.description}</p>
                </a>
            `;
        });
        html += '</div>';

        searchResults.html(html);
    }

    // Language switcher
    function setupLanguageSwitcher() {
        window.switchLanguage = function(lang) {
            const url = new URL(window.location);
            url.searchParams.set('lang', lang);
            window.location.href = url.toString();
        };
    }

    // Utility functions
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas ${iconClass}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Remove existing alerts
        $('.alert').remove();
        
        // Add new alert to top of page
        $('body').prepend(alertHtml);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Handle page load
    function handlePageLoad() {
        // Hide loading spinner if exists
        $('.page-loader').fadeOut();
        
        // Initialize page-specific functionality
        const page = $('body').data('page');
        
        switch(page) {
            case 'home':
                initHomePage();
                break;
            case 'contact':
                initContactPage();
                break;
            case 'services':
                initServicesPage();
                break;
        }
    }

    // Page-specific initializations
    function initHomePage() {
        // Hero slider or animations
        initHeroAnimations();
    }

    function initContactPage() {
        // Initialize map if needed
        initContactMap();
    }

    function initServicesPage() {
        // Service filtering or animations
        initServiceFilters();
    }

    function initHeroAnimations() {
        // Add any hero-specific animations
    }

    function initContactMap() {
        // Initialize Google Maps or other map service
    }

    function initServiceFilters() {
        // Add service filtering functionality
    }

    // Expose global functions
    window.WebsiteDeveloper0002 = {
        showAlert: showAlert,
        switchLanguage: window.switchLanguage
    };

})(jQuery);

// Vanilla JS for non-jQuery dependent features
document.addEventListener('DOMContentLoaded', function() {
    // Cookie consent
    initCookieConsent();
    
    // Performance monitoring
    initPerformanceMonitoring();
});

function initCookieConsent() {
    const consent = localStorage.getItem('cookieConsent');
    const cookieBanner = document.getElementById('cookieConsent');
    
    if (!consent && cookieBanner) {
        cookieBanner.style.display = 'block';
    }
}

function acceptCookies() {
    localStorage.setItem('cookieConsent', 'accepted');
    document.getElementById('cookieConsent').style.display = 'none';
}

function declineCookies() {
    localStorage.setItem('cookieConsent', 'declined');
    document.getElementById('cookieConsent').style.display = 'none';
}

function initPerformanceMonitoring() {
    // Basic performance monitoring
    window.addEventListener('load', function() {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log('Page load time:', loadTime + 'ms');
        
        // Send to analytics if needed
        if (typeof gtag !== 'undefined') {
            gtag('event', 'timing_complete', {
                name: 'load',
                value: loadTime
            });
        }
    });
}
