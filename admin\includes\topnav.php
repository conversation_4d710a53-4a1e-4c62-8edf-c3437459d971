<div class="topnav">
    <div class="topnav-left">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-toggle" id="mobileToggle">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Breadcrumb -->
        <nav class="breadcrumb-nav">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="dashboard.php">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </li>
                <?php
                $currentPage = basename($_SERVER['PHP_SELF'], '.php');
                $pageTitle = ucfirst(str_replace('-', ' ', $currentPage));
                
                if ($currentPage !== 'dashboard'):
                ?>
                <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                <?php endif; ?>
            </ol>
        </nav>
    </div>
    
    <div class="topnav-right">
        <!-- Language Switcher -->
        <div class="nav-item dropdown">
            <button class="nav-btn dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-globe"></i>
                <span class="d-none d-md-inline"><?php echo $lang === 'en' ? 'English' : 'हिंदी'; ?></span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item <?php echo $lang === 'en' ? 'active' : ''; ?>" 
                       href="?lang=en">
                        <i class="fas fa-flag-usa"></i> English
                    </a>
                </li>
                <li>
                    <a class="dropdown-item <?php echo $lang === 'hi' ? 'active' : ''; ?>" 
                       href="?lang=hi">
                        <i class="fas fa-flag"></i> हिंदी
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Notifications -->
        <div class="nav-item dropdown">
            <button class="nav-btn dropdown-toggle position-relative" data-bs-toggle="dropdown">
                <i class="fas fa-bell"></i>
                <?php if ($stats['new_messages'] > 0): ?>
                    <span class="notification-badge"><?php echo $stats['new_messages']; ?></span>
                <?php endif; ?>
            </button>
            <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                <div class="dropdown-header">
                    <h6 class="mb-0">Notifications</h6>
                    <?php if ($stats['new_messages'] > 0): ?>
                        <span class="badge bg-primary"><?php echo $stats['new_messages']; ?> new</span>
                    <?php endif; ?>
                </div>
                <div class="dropdown-body">
                    <?php if (!empty($recentMessages)): ?>
                        <?php foreach (array_slice($recentMessages, 0, 3) as $message): ?>
                            <a href="messages.php?id=<?php echo $message['id']; ?>" class="notification-item">
                                <div class="notification-icon">
                                    <i class="fas fa-envelope text-primary"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="notification-title">New Message</h6>
                                    <p class="notification-text">
                                        From: <?php echo htmlspecialchars($message['name']); ?>
                                    </p>
                                    <small class="notification-time">
                                        <?php echo formatDate($message['created_at'], 'M j, g:i A'); ?>
                                    </small>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="notification-item text-center">
                            <p class="text-muted mb-0">No new notifications</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="dropdown-footer">
                    <a href="messages.php" class="btn btn-sm btn-primary w-100">View All Messages</a>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="nav-item dropdown">
            <button class="nav-btn dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus"></i>
                <span class="d-none d-md-inline">Quick Add</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item" href="users.php?action=add">
                        <i class="fas fa-user-plus"></i> Add User
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="pages.php?action=add">
                        <i class="fas fa-file-plus"></i> Add Page
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="services.php?action=add">
                        <i class="fas fa-cog"></i> Add Service
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="blog.php?action=add">
                        <i class="fas fa-blog"></i> Add Blog Post
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- User Menu -->
        <div class="nav-item dropdown">
            <button class="nav-btn dropdown-toggle user-menu" data-bs-toggle="dropdown">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-info d-none d-md-block">
                    <span class="user-name"><?php echo $_SESSION['full_name']; ?></span>
                    <small class="user-role">Administrator</small>
                </div>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li class="dropdown-header">
                    <div class="user-details">
                        <h6 class="mb-0"><?php echo $_SESSION['full_name']; ?></h6>
                        <small class="text-muted"><?php echo $_SESSION['email']; ?></small>
                    </div>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="profile.php">
                        <i class="fas fa-user"></i> My Profile
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="activity.php">
                        <i class="fas fa-history"></i> Activity Log
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="../?lang=<?php echo $lang; ?>" target="_blank">
                        <i class="fas fa-external-link-alt"></i> View Website
                    </a>
                </li>
                <li>
                    <a class="dropdown-item text-danger" href="../auth/logout.php?lang=<?php echo $lang; ?>">
                        <i class="fas fa-sign-out-alt"></i> <?php echo $translations['nav_logout']; ?>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Search</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="search-form">
                    <input type="text" class="form-control form-control-lg" placeholder="Search users, pages, messages..." id="globalSearch">
                </div>
                <div class="search-results mt-3" id="searchResults">
                    <!-- Search results will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global search functionality
    const searchInput = document.getElementById('globalSearch');
    const searchResults = document.getElementById('searchResults');
    
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            } else {
                searchResults.innerHTML = '';
            }
        });
    }
    
    function performSearch(query) {
        // Show loading
        searchResults.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
        
        // Perform AJAX search
        fetch('api/search.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({query: query})
        })
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('Search error:', error);
            searchResults.innerHTML = '<div class="text-center py-3 text-danger">Search failed. Please try again.</div>';
        });
    }
    
    function displaySearchResults(results) {
        if (results.length === 0) {
            searchResults.innerHTML = '<div class="text-center py-3 text-muted">No results found.</div>';
            return;
        }
        
        let html = '<div class="list-group">';
        results.forEach(result => {
            html += `
                <a href="${result.url}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${result.title}</h6>
                        <small class="text-muted">${result.type}</small>
                    </div>
                    <p class="mb-1">${result.description}</p>
                </a>
            `;
        });
        html += '</div>';
        
        searchResults.innerHTML = html;
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchModal = new bootstrap.Modal(document.getElementById('searchModal'));
            searchModal.show();
            setTimeout(() => {
                searchInput.focus();
            }, 300);
        }
    });
});
</script>
