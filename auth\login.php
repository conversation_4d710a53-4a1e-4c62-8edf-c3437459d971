<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        redirect('../admin/dashboard.php');
    } else {
        redirect('../client/dashboard.php');
    }
}

// Get current language
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
if (!in_array($lang, ['en', 'hi'])) {
    $lang = 'en';
}

// Load language file
$translations = loadLanguage($lang);

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                
                // Set remember me cookie
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                    
                    // Store token in database (you might want to create a remember_tokens table)
                    $stmt = $pdo->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                    $stmt->execute([$token, $user['id']]);
                }
                
                // Log activity
                logActivity($user['id'], 'login', 'User logged in');
                
                // Redirect based on role
                if ($user['role'] === 'admin') {
                    redirect('../admin/dashboard.php');
                } else {
                    redirect('../client/dashboard.php');
                }
            } else {
                $error = $translations['auth_login_error'];
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translations['auth_login']; ?> - <?php echo $translations['site_title']; ?></title>
    <meta name="description" content="Login to your account">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card">
                        <!-- Logo -->
                        <div class="auth-header text-center mb-4">
                            <a href="../?lang=<?php echo $lang; ?>">
                                <img src="../assets/images/logo.png" alt="Logo" height="60">
                            </a>
                            <h2 class="auth-title mt-3"><?php echo $translations['auth_login']; ?></h2>
                            <p class="auth-subtitle">Welcome back! Please sign in to your account.</p>
                        </div>
                        
                        <!-- Language Switcher -->
                        <div class="language-switcher text-center mb-4">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="switchLanguage('en')" 
                                    <?php echo $lang === 'en' ? 'disabled' : ''; ?>>
                                English
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="switchLanguage('hi')" 
                                    <?php echo $lang === 'hi' ? 'disabled' : ''; ?>>
                                हिंदी
                            </button>
                        </div>
                        
                        <!-- Error/Success Messages -->
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Login Form -->
                        <form method="POST" class="auth-form">
                            <div class="form-group mb-3">
                                <label for="username" class="form-label">
                                    <?php echo $translations['auth_username']; ?> / <?php echo $translations['auth_email']; ?>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="password" class="form-label"><?php echo $translations['auth_password']; ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        <?php echo $translations['auth_remember_me']; ?>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt"></i> <?php echo $translations['auth_login']; ?>
                            </button>
                        </form>
                        
                        <!-- Additional Links -->
                        <div class="auth-links text-center">
                            <p class="mb-2">
                                <a href="forgot-password.php?lang=<?php echo $lang; ?>" class="text-primary">
                                    <?php echo $translations['auth_forgot_password']; ?>
                                </a>
                            </p>
                            <p class="mb-0">
                                <?php echo $translations['auth_no_account']; ?> 
                                <a href="register.php?lang=<?php echo $lang; ?>" class="text-primary">
                                    <?php echo $translations['auth_register']; ?>
                                </a>
                            </p>
                        </div>
                        
                        <!-- Demo Credentials -->
                        <div class="demo-credentials mt-4">
                            <h6>Demo Credentials:</h6>
                            <p class="mb-1"><strong>Admin:</strong> admin / admin123</p>
                            <p class="mb-0"><strong>Client:</strong> client / client123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }
        
        function switchLanguage(lang) {
            const url = new URL(window.location);
            url.searchParams.set('lang', lang);
            window.location.href = url.toString();
        }
        
        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
