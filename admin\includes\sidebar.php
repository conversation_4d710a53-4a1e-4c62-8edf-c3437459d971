<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="../assets/images/logo-white.png" alt="Logo" height="40">
            <span class="sidebar-title">Admin Panel</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="sidebar-content">
        <!-- User Info -->
        <div class="sidebar-user">
            <div class="user-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-info">
                <h6 class="user-name"><?php echo $_SESSION['full_name']; ?></h6>
                <small class="user-role">Administrator</small>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span class="nav-text"><?php echo $translations['admin_dashboard']; ?></span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="users.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'users.php' ? 'active' : ''; ?>">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text"><?php echo $translations['admin_users']; ?></span>
                        <?php if ($stats['total_clients'] > 0): ?>
                            <span class="nav-badge"><?php echo $stats['total_clients']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link nav-dropdown-toggle">
                        <i class="nav-icon fas fa-file-alt"></i>
                        <span class="nav-text">Content Management</span>
                        <i class="nav-arrow fas fa-chevron-down"></i>
                    </a>
                    <ul class="nav-dropdown">
                        <li class="nav-item">
                            <a href="pages.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'pages.php' ? 'active' : ''; ?>">
                                <i class="nav-icon fas fa-file"></i>
                                <span class="nav-text"><?php echo $translations['admin_pages']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="services.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'services.php' ? 'active' : ''; ?>">
                                <i class="nav-icon fas fa-cogs"></i>
                                <span class="nav-text"><?php echo $translations['admin_services']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="blog.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'blog.php' ? 'active' : ''; ?>">
                                <i class="nav-icon fas fa-blog"></i>
                                <span class="nav-text">Blog Posts</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="messages.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'messages.php' ? 'active' : ''; ?>">
                        <i class="nav-icon fas fa-envelope"></i>
                        <span class="nav-text"><?php echo $translations['admin_messages']; ?></span>
                        <?php if ($stats['new_messages'] > 0): ?>
                            <span class="nav-badge bg-danger"><?php echo $stats['new_messages']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link nav-dropdown-toggle">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span class="nav-text">Analytics</span>
                        <i class="nav-arrow fas fa-chevron-down"></i>
                    </a>
                    <ul class="nav-dropdown">
                        <li class="nav-item">
                            <a href="analytics.php" class="nav-link">
                                <i class="nav-icon fas fa-chart-line"></i>
                                <span class="nav-text">Website Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="reports.php" class="nav-link">
                                <i class="nav-icon fas fa-file-chart"></i>
                                <span class="nav-text">Reports</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link nav-dropdown-toggle">
                        <i class="nav-icon fas fa-tools"></i>
                        <span class="nav-text">Tools</span>
                        <i class="nav-arrow fas fa-chevron-down"></i>
                    </a>
                    <ul class="nav-dropdown">
                        <li class="nav-item">
                            <a href="file-manager.php" class="nav-link">
                                <i class="nav-icon fas fa-folder"></i>
                                <span class="nav-text">File Manager</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="backup.php" class="nav-link">
                                <i class="nav-icon fas fa-download"></i>
                                <span class="nav-text">Backup</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="logs.php" class="nav-link">
                                <i class="nav-icon fas fa-list"></i>
                                <span class="nav-text">System Logs</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="settings.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>">
                        <i class="nav-icon fas fa-cog"></i>
                        <span class="nav-text"><?php echo $translations['admin_settings']; ?></span>
                    </a>
                </li>
                
                <li class="nav-divider"></li>
                
                <li class="nav-item">
                    <a href="../?lang=<?php echo $lang; ?>" class="nav-link" target="_blank">
                        <i class="nav-icon fas fa-external-link-alt"></i>
                        <span class="nav-text">View Website</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="../auth/logout.php?lang=<?php echo $lang; ?>" class="nav-link">
                        <i class="nav-icon fas fa-sign-out-alt"></i>
                        <span class="nav-text"><?php echo $translations['nav_logout']; ?></span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="footer-info">
            <small class="text-muted">
                <i class="fas fa-clock"></i> 
                Last login: <?php echo date('M j, g:i A'); ?>
            </small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar toggle functionality
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const mobileToggle = document.getElementById('mobileToggle');
    
    // Toggle sidebar
    function toggleSidebar() {
        sidebar.classList.toggle('active');
        sidebarOverlay.classList.toggle('active');
        document.body.classList.toggle('sidebar-open');
    }
    
    // Close sidebar
    function closeSidebar() {
        sidebar.classList.remove('active');
        sidebarOverlay.classList.remove('active');
        document.body.classList.remove('sidebar-open');
    }
    
    // Event listeners
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', closeSidebar);
    }
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', toggleSidebar);
    }
    
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeSidebar);
    }
    
    // Dropdown functionality
    const dropdownToggles = document.querySelectorAll('.nav-dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdown = this.nextElementSibling;
            const arrow = this.querySelector('.nav-arrow');
            
            // Close other dropdowns
            dropdownToggles.forEach(otherToggle => {
                if (otherToggle !== this) {
                    const otherDropdown = otherToggle.nextElementSibling;
                    const otherArrow = otherToggle.querySelector('.nav-arrow');
                    otherDropdown.style.display = 'none';
                    otherArrow.style.transform = 'rotate(0deg)';
                }
            });
            
            // Toggle current dropdown
            if (dropdown.style.display === 'block') {
                dropdown.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
            } else {
                dropdown.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
            }
        });
    });
    
    // Auto-close sidebar on mobile when clicking nav links
    const navLinks = document.querySelectorAll('.nav-link:not(.nav-dropdown-toggle)');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        });
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
        }
    });
});
</script>
