<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is client
requireLogin();

if (isAdmin()) {
    redirect('../admin/dashboard.php');
}

// Get current language
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
if (!in_array($lang, ['en', 'hi'])) {
    $lang = 'en';
}

// Load language file
$translations = loadLanguage($lang);

// Get user's projects/orders (you can implement this based on your needs)
try {
    $stmt = $pdo->prepare("SELECT * FROM user_projects WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$_SESSION['user_id']]);
    $recentProjects = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentProjects = [];
}

// Get user's messages
try {
    $stmt = $pdo->prepare("SELECT * FROM contact_messages WHERE email = ? <PERSON><PERSON><PERSON>Y created_at DESC LIMIT 5");
    $stmt->execute([$_SESSION['email']]);
    $userMessages = $stmt->fetchAll();
} catch (PDOException $e) {
    $userMessages = [];
}

// Get user stats
$userStats = [
    'total_projects' => count($recentProjects),
    'total_messages' => count($userMessages),
    'pending_invoices' => 0, // Implement based on your needs
    'support_tickets' => 0   // Implement based on your needs
];
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translations['client_dashboard']; ?> - <?php echo $translations['site_title']; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/client.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="client-body">
    <!-- Header -->
    <header class="client-header">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="../?lang=<?php echo $lang; ?>">
                    <img src="../assets/images/logo.png" alt="Logo" height="40">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> <?php echo $translations['client_dashboard']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="projects.php">
                                <i class="fas fa-project-diagram"></i> <?php echo $translations['client_projects']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> <?php echo $translations['client_invoices']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="support.php">
                                <i class="fas fa-headset"></i> <?php echo $translations['client_support']; ?>
                            </a>
                        </li>
                    </ul>
                    
                    <div class="navbar-nav">
                        <!-- Language Switcher -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-globe"></i> <?php echo $lang === 'en' ? 'English' : 'हिंदी'; ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?lang=en">English</a></li>
                                <li><a class="dropdown-item" href="?lang=hi">हिंदी</a></li>
                            </ul>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['full_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user"></i> <?php echo $translations['client_profile']; ?>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../?lang=<?php echo $lang; ?>" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> View Website
                                </a></li>
                                <li><a class="dropdown-item" href="../auth/logout.php?lang=<?php echo $lang; ?>">
                                    <i class="fas fa-sign-out-alt"></i> <?php echo $translations['nav_logout']; ?>
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Main Content -->
    <main class="client-main">
        <div class="container py-4">
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-card">
                        <h1 class="welcome-title">
                            <?php echo $translations['dashboard_welcome']; ?>, <?php echo $_SESSION['full_name']; ?>!
                        </h1>
                        <p class="welcome-subtitle">
                            Here's an overview of your account and recent activity.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-primary text-white">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $userStats['total_projects']; ?></h3>
                            <p class="stat-label">Total Projects</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-success text-white">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $userStats['total_messages']; ?></h3>
                            <p class="stat-label">Messages Sent</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-warning text-white">
                        <div class="stat-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $userStats['pending_invoices']; ?></h3>
                            <p class="stat-label">Pending Invoices</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-info text-white">
                        <div class="stat-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number"><?php echo $userStats['support_tickets']; ?></h3>
                            <p class="stat-label">Support Tickets</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-bolt"></i> Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="../contact?lang=<?php echo $lang; ?>" class="quick-action-btn">
                                        <i class="fas fa-plus"></i>
                                        <span>New Project</span>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="support.php" class="quick-action-btn">
                                        <i class="fas fa-headset"></i>
                                        <span>Get Support</span>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="invoices.php" class="quick-action-btn">
                                        <i class="fas fa-file-invoice"></i>
                                        <span>View Invoices</span>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="profile.php" class="quick-action-btn">
                                        <i class="fas fa-user-edit"></i>
                                        <span>Edit Profile</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <!-- Recent Projects -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-project-diagram"></i> Recent Projects
                            </h5>
                            <a href="projects.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentProjects)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Project Name</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentProjects as $project): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($project['name']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $project['status'] === 'completed' ? 'success' : ($project['status'] === 'in_progress' ? 'warning' : 'secondary'); ?>">
                                                            <?php echo ucfirst($project['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($project['created_at'], 'M j, Y'); ?></td>
                                                    <td>
                                                        <a href="projects.php?id=<?php echo $project['id']; ?>" class="btn btn-sm btn-outline-primary">View</a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                    <h5>No Projects Yet</h5>
                                    <p class="text-muted">Start your first project with us!</p>
                                    <a href="../contact?lang=<?php echo $lang; ?>" class="btn btn-primary">Start New Project</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Messages -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-envelope"></i> Recent Messages
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($userMessages)): ?>
                                <div class="message-list">
                                    <?php foreach (array_slice($userMessages, 0, 3) as $message): ?>
                                        <div class="message-item">
                                            <div class="message-header">
                                                <strong><?php echo htmlspecialchars($message['subject'] ?? 'No Subject'); ?></strong>
                                                <small class="text-muted">
                                                    <?php echo formatDate($message['created_at'], 'M j'); ?>
                                                </small>
                                            </div>
                                            <p class="message-preview">
                                                <?php echo substr(htmlspecialchars($message['message']), 0, 80) . '...'; ?>
                                            </p>
                                            <span class="badge bg-<?php echo $message['status'] === 'replied' ? 'success' : ($message['status'] === 'read' ? 'warning' : 'secondary'); ?>">
                                                <?php echo ucfirst($message['status']); ?>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-envelope fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">No messages yet.</p>
                                    <a href="../contact?lang=<?php echo $lang; ?>" class="btn btn-sm btn-primary">Send Message</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="client-footer bg-light py-3">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> WebsiteDeveloper0002. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../?lang=<?php echo $lang; ?>" class="text-muted me-3">Visit Website</a>
                    <a href="../contact?lang=<?php echo $lang; ?>" class="text-muted">Contact Support</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript Files -->
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
