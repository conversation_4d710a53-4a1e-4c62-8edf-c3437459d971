<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get current language
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
if (!in_array($lang, ['en', 'hi'])) {
    $lang = 'en';
}

// Load language file
$translations = loadLanguage($lang);

// Get page content
$page = isset($_GET['page']) ? $_GET['page'] : 'home';
$pageContent = getPageContent($page, $lang);
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageContent['title'] ?? $translations['site_title']; ?></title>
    <meta name="description" content="<?php echo $pageContent['description'] ?? $translations['site_description']; ?>">
    <meta name="keywords" content="<?php echo $pageContent['keywords'] ?? $translations['site_keywords']; ?>">
    
    <!-- SEO Meta Tags -->
    <meta property="og:title" content="<?php echo $pageContent['title'] ?? $translations['site_title']; ?>">
    <meta property="og:description" content="<?php echo $pageContent['description'] ?? $translations['site_description']; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo getCurrentUrl(); ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "<?php echo $translations['site_title']; ?>",
        "url": "<?php echo BASE_URL; ?>",
        "logo": "<?php echo BASE_URL; ?>/assets/images/logo.png",
        "description": "<?php echo $translations['site_description']; ?>",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "IN"
        }
    }
    </script>
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <?php
        switch($page) {
            case 'home':
                include 'pages/home.php';
                break;
            case 'about':
                include 'pages/about.php';
                break;
            case 'services':
                include 'pages/services.php';
                break;
            case 'pricing':
                include 'pages/pricing.php';
                break;
            case 'contact':
                include 'pages/contact.php';
                break;
            case 'blog':
                include 'pages/blog.php';
                break;
            case 'portfolio':
                include 'pages/portfolio.php';
                break;
            default:
                include 'pages/404.php';
        }
        ?>
    </main>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <!-- Language Switcher Script -->
    <script>
        function switchLanguage(lang) {
            const url = new URL(window.location);
            url.searchParams.set('lang', lang);
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
