<!-- Services Page -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title"><?php echo $translations['nav_services']; ?></h1>
                <p class="page-subtitle"><?php echo $translations['services_subtitle']; ?></p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL; ?>?lang=<?php echo $lang; ?>" class="text-white">
                                <?php echo $translations['nav_home']; ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white"><?php echo $translations['nav_services']; ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Services Grid -->
<section class="services-grid py-5">
    <div class="container">
        <div class="row">
            <?php
            $services = getServices($lang);
            
            // Default services if none in database
            $defaultServices = [
                [
                    'id' => 'web-design',
                    'title' => $translations['service_web_design'],
                    'description' => 'Create stunning, responsive websites that captivate your audience and drive conversions. Our designs are modern, user-friendly, and optimized for all devices.',
                    'icon' => 'fas fa-paint-brush',
                    'price' => '15000',
                    'features' => 'Responsive Design, Modern UI/UX, Cross-browser Compatibility, Mobile Optimization'
                ],
                [
                    'id' => 'web-development',
                    'title' => $translations['service_web_development'],
                    'description' => 'Build powerful web applications using the latest technologies. From simple websites to complex enterprise solutions.',
                    'icon' => 'fas fa-code',
                    'price' => '25000',
                    'features' => 'Custom Development, Database Integration, API Development, Performance Optimization'
                ],
                [
                    'id' => 'ecommerce',
                    'title' => $translations['service_ecommerce'],
                    'description' => 'Complete e-commerce solutions to help you sell online. Secure payment gateways, inventory management, and more.',
                    'icon' => 'fas fa-shopping-cart',
                    'price' => '35000',
                    'features' => 'Shopping Cart, Payment Gateway, Inventory Management, Order Tracking'
                ],
                [
                    'id' => 'seo',
                    'title' => $translations['service_seo'],
                    'description' => 'Improve your search engine rankings and drive organic traffic to your website with our proven SEO strategies.',
                    'icon' => 'fas fa-search',
                    'price' => '10000',
                    'features' => 'Keyword Research, On-page SEO, Technical SEO, Content Optimization'
                ],
                [
                    'id' => 'maintenance',
                    'title' => $translations['service_maintenance'],
                    'description' => 'Keep your website running smoothly with our comprehensive maintenance and support services.',
                    'icon' => 'fas fa-tools',
                    'price' => '5000',
                    'features' => 'Regular Updates, Security Monitoring, Backup Services, Technical Support'
                ],
                [
                    'id' => 'hosting',
                    'title' => $translations['service_hosting'],
                    'description' => 'Reliable and secure web hosting solutions with 99.9% uptime guarantee and 24/7 support.',
                    'icon' => 'fas fa-server',
                    'price' => '3000',
                    'features' => 'SSD Storage, SSL Certificate, Daily Backups, 24/7 Support'
                ]
            ];
            
            $displayServices = !empty($services) ? $services : $defaultServices;
            
            foreach ($displayServices as $service):
            ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card-detailed" id="<?php echo $service['id'] ?? ''; ?>">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="<?php echo $service['icon'] ?? 'fas fa-cog'; ?>"></i>
                        </div>
                        <h3 class="service-title"><?php echo $service['title']; ?></h3>
                        <?php if (isset($service['price']) && $service['price']): ?>
                            <div class="service-price">
                                <span class="price-currency"><?php echo $translations['pricing_currency']; ?></span>
                                <span class="price-amount"><?php echo number_format($service['price']); ?></span>
                                <span class="price-period">/project</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="service-body">
                        <p class="service-description"><?php echo $service['description']; ?></p>
                        
                        <?php if (isset($service['features']) && $service['features']): ?>
                            <div class="service-features">
                                <h5>Features:</h5>
                                <ul class="feature-list">
                                    <?php
                                    $features = explode(',', $service['features']);
                                    foreach ($features as $feature):
                                    ?>
                                        <li><i class="fas fa-check text-success"></i> <?php echo trim($feature); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="service-footer">
                        <a href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>&service=<?php echo urlencode($service['title']); ?>" 
                           class="btn btn-primary w-100">
                            <?php echo $translations['get_quote']; ?>
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="process-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Our Development Process</h2>
                <p class="section-subtitle">We follow a proven methodology to deliver exceptional results</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <div class="step-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h4 class="step-title">Discovery</h4>
                    <p class="step-description">We understand your requirements, goals, and target audience to create the perfect strategy.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step">
                    <div class="step-number">2</div>
                    <div class="step-icon">
                        <i class="fas fa-pencil-ruler"></i>
                    </div>
                    <h4 class="step-title">Design</h4>
                    <p class="step-description">Create wireframes and mockups that align with your brand and user experience goals.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step">
                    <div class="step-number">3</div>
                    <div class="step-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h4 class="step-title">Development</h4>
                    <p class="step-description">Build your website using the latest technologies and best practices for optimal performance.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step">
                    <div class="step-number">4</div>
                    <div class="step-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4 class="step-title">Launch</h4>
                    <p class="step-description">Test thoroughly, deploy to production, and provide ongoing support and maintenance.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="technologies-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Technologies We Use</h2>
                <p class="section-subtitle">We work with the latest and most reliable technologies</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/html5.png" alt="HTML5" class="tech-logo">
                    <h6 class="tech-name">HTML5</h6>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/css3.png" alt="CSS3" class="tech-logo">
                    <h6 class="tech-name">CSS3</h6>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/javascript.png" alt="JavaScript" class="tech-logo">
                    <h6 class="tech-name">JavaScript</h6>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/php.png" alt="PHP" class="tech-logo">
                    <h6 class="tech-name">PHP</h6>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/mysql.png" alt="MySQL" class="tech-logo">
                    <h6 class="tech-name">MySQL</h6>
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-4 mb-4">
                <div class="tech-item">
                    <img src="../assets/images/tech/bootstrap.png" alt="Bootstrap" class="tech-logo">
                    <h6 class="tech-name">Bootstrap</h6>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="cta-title">Ready to Get Started?</h2>
                <p class="cta-description">Let's discuss your project and create something amazing together.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>" class="btn btn-light btn-lg">
                    <?php echo $translations['contact_title']; ?>
                </a>
            </div>
        </div>
    </div>
</section>
