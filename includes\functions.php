<?php
// Common functions

function loadLanguage($lang = 'en') {
    $langFile = "languages/{$lang}.php";
    if (file_exists($langFile)) {
        return include $langFile;
    }
    return include "languages/en.php"; // fallback to English
}

function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

function getPageContent($page, $lang = 'en') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM pages WHERE page_key = ? AND language = ? AND status = 'active'");
        $stmt->execute([$page, $lang]);
        return $stmt->fetch();
    } catch(PDOException $e) {
        return null;
    }
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: auth/login.php');
        exit;
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: auth/login.php');
        exit;
    }
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function generateSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

function uploadFile($file, $uploadDir = 'uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new RuntimeException('Invalid parameters.');
    }

    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new RuntimeException('No file sent.');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new RuntimeException('Exceeded filesize limit.');
        default:
            throw new RuntimeException('Unknown errors.');
    }

    if ($file['size'] > 5000000) { // 5MB limit
        throw new RuntimeException('Exceeded filesize limit.');
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->file($file['tmp_name']);
    
    $allowedTypes = [
        'jpg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    $ext = array_search($mimeType, $allowedTypes, true);
    if ($ext === false) {
        throw new RuntimeException('Invalid file format.');
    }

    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $filename = sprintf('%s.%s', sha1_file($file['tmp_name']), $ext);
    $destination = $uploadDir . $filename;

    if (!move_uploaded_file($file['tmp_name'], $destination)) {
        throw new RuntimeException('Failed to move uploaded file.');
    }

    return $destination;
}

function sendEmail($to, $subject, $message, $from = null) {
    if ($from === null) {
        $from = ADMIN_EMAIL;
    }
    
    $headers = "From: $from\r\n";
    $headers .= "Reply-To: $from\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

function getServices($lang = 'en') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT *, 
                              CASE WHEN ? = 'hi' THEN title_hi ELSE title_en END as title,
                              CASE WHEN ? = 'hi' THEN description_hi ELSE description_en END as description,
                              CASE WHEN ? = 'hi' THEN features_hi ELSE features_en END as features
                              FROM services WHERE status = 'active' ORDER BY sort_order ASC");
        $stmt->execute([$lang, $lang, $lang]);
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

function getContactMessages($status = null) {
    global $pdo;
    
    try {
        if ($status) {
            $stmt = $pdo->prepare("SELECT * FROM contact_messages WHERE status = ? ORDER BY created_at DESC");
            $stmt->execute([$status]);
        } else {
            $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC");
        }
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        return [];
    }
}

function updateContactMessageStatus($id, $status) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("UPDATE contact_messages SET status = ? WHERE id = ?");
        return $stmt->execute([$status, $id]);
    } catch(PDOException $e) {
        return false;
    }
}

function getUserStats() {
    global $pdo;
    
    try {
        $stats = [];
        
        // Total users
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'client'");
        $stats['total_clients'] = $stmt->fetch()['total'];
        
        // Total messages
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM contact_messages");
        $stats['total_messages'] = $stmt->fetch()['total'];
        
        // New messages
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM contact_messages WHERE status = 'new'");
        $stats['new_messages'] = $stmt->fetch()['total'];
        
        // Total services
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM services WHERE status = 'active'");
        $stats['total_services'] = $stmt->fetch()['total'];
        
        return $stats;
    } catch(PDOException $e) {
        return [
            'total_clients' => 0,
            'total_messages' => 0,
            'new_messages' => 0,
            'total_services' => 0
        ];
    }
}

function logActivity($user_id, $action, $details = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([
            $user_id,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch(PDOException $e) {
        return false;
    }
}

function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function redirect($url, $statusCode = 302) {
    header('Location: ' . $url, true, $statusCode);
    exit;
}

function flashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

function hasFlashMessage($type) {
    return isset($_SESSION['flash'][$type]);
}

// Get user projects (placeholder function)
function getUserProjects($userId, $limit = null) {
    global $pdo;

    try {
        $sql = "SELECT * FROM user_projects WHERE user_id = ? ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get user projects error: " . $e->getMessage());
        return [];
    }
}

// Generate random string
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

// Check if email exists
function emailExists($email) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("Email exists check error: " . $e->getMessage());
        return false;
    }
}

?>
