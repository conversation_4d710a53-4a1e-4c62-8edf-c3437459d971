/* Client Dashboard CSS */

.client-body {
    background-color: #f8f9fa;
    font-family: 'Poppins', sans-serif;
}

/* Header */
.client-header {
    position: sticky;
    top: 0;
    z-index: 1020;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.client-header .navbar-brand img {
    max-height: 40px;
}

.client-header .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.client-header .nav-link:hover,
.client-header .nav-link.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.client-header .nav-link i {
    margin-right: 0.5rem;
}

/* Main Content */
.client-main {
    min-height: calc(100vh - 140px);
    padding-top: 1rem;
}

/* Welcome Card */
.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 1rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Stat Cards */
.stat-card {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0;
    font-weight: 500;
}

/* Quick Actions */
.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 0.75rem;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    height: 120px;
}

.quick-action-btn:hover {
    border-color: var(--bs-primary);
    color: var(--bs-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.quick-action-btn span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    border-radius: 1rem 1rem 0 0 !important;
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.card-title i {
    margin-right: 0.5rem;
    color: var(--bs-primary);
}

.card-body {
    padding: 1.5rem;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Message List */
.message-list {
    max-height: 300px;
    overflow-y: auto;
}

.message-item {
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.message-item:last-child {
    border-bottom: none;
}

.message-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-header strong {
    font-size: 0.9rem;
    color: #495057;
}

.message-preview {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

/* Empty States */
.text-center .fa-3x {
    opacity: 0.3;
}

.text-center .fa-2x {
    opacity: 0.3;
}

/* Footer */
.client-footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

.client-footer .text-muted {
    color: #6c757d !important;
    text-decoration: none;
}

.client-footer .text-muted:hover {
    color: var(--bs-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-card {
        padding: 1.5rem;
        text-align: center;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
        padding: 1.25rem;
    }
    
    .stat-icon {
        font-size: 2rem;
        margin-right: 0.75rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .quick-action-btn {
        height: 100px;
        padding: 1rem;
    }
    
    .quick-action-btn i {
        font-size: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-header .btn {
        margin-top: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .message-item {
        padding: 0.75rem 0;
    }
    
    .client-header .nav-link {
        padding: 0.5rem 0.75rem;
        margin: 0.125rem;
    }
    
    .client-header .nav-link i {
        margin-right: 0.25rem;
    }
}

@media (max-width: 576px) {
    .client-main {
        padding-top: 0.5rem;
    }
    
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .welcome-card {
        padding: 1rem;
    }
    
    .welcome-title {
        font-size: 1.25rem;
    }
    
    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .quick-action-btn {
        height: 80px;
        padding: 0.75rem;
    }
    
    .quick-action-btn i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }
    
    .quick-action-btn span {
        font-size: 0.8rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .client-body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .client-header {
        background: #2d3748;
        border-bottom-color: #4a5568;
    }
    
    .card {
        background: #2d3748;
        color: #e9ecef;
    }
    
    .card-header {
        background: #2d3748;
        border-bottom-color: #4a5568;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table th,
    .table td {
        border-color: #4a5568;
    }
    
    .table-hover tbody tr:hover {
        background-color: #374151;
    }
    
    .quick-action-btn {
        background: #2d3748;
        border-color: #4a5568;
        color: #e9ecef;
    }
    
    .client-footer {
        background: #2d3748 !important;
        border-top-color: #4a5568;
    }
}

/* Print Styles */
@media print {
    .client-header,
    .client-footer,
    .quick-action-btn {
        display: none !important;
    }
    
    .client-main {
        padding-top: 0;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .stat-card {
        background: none !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
}
