<footer class="footer bg-dark text-light">
    <div class="container">
        <!-- Main Footer Content -->
        <div class="row py-5">
            <!-- Company Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="footer-widget">
                    <img src="assets/images/logo-white.png" alt="<?php echo $translations['site_title']; ?>" height="50" class="mb-3">
                    <p class="mb-3"><?php echo $translations['footer_description']; ?></p>
                    
                    <!-- Social Links -->
                    <div class="social-links">
                        <h6 class="mb-3"><?php echo $translations['footer_follow_us']; ?></h6>
                        <a href="#" class="social-link me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link me-3"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link me-3"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-widget">
                    <h5 class="mb-3"><?php echo $translations['footer_quick_links']; ?></h5>
                    <ul class="footer-links">
                        <li><a href="<?php echo BASE_URL; ?>?lang=<?php echo $lang; ?>"><?php echo $translations['nav_home']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/about?lang=<?php echo $lang; ?>"><?php echo $translations['nav_about']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>"><?php echo $translations['nav_services']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/pricing?lang=<?php echo $lang; ?>"><?php echo $translations['nav_pricing']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/portfolio?lang=<?php echo $lang; ?>"><?php echo $translations['nav_portfolio']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>"><?php echo $translations['nav_contact']; ?></a></li>
                    </ul>
                </div>
            </div>

            <!-- Services -->
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="footer-widget">
                    <h5 class="mb-3"><?php echo $translations['footer_services_title']; ?></h5>
                    <ul class="footer-links">
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#web-design"><?php echo $translations['service_web_design']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#web-development"><?php echo $translations['service_web_development']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#ecommerce"><?php echo $translations['service_ecommerce']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#seo"><?php echo $translations['service_seo']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#maintenance"><?php echo $translations['service_maintenance']; ?></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#hosting"><?php echo $translations['service_hosting']; ?></a></li>
                    </ul>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="footer-widget">
                    <h5 class="mb-3"><?php echo $translations['footer_contact_info']; ?></h5>
                    <div class="contact-info">
                        <div class="contact-item mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <div>
                                <strong><?php echo $translations['contact_address']; ?>:</strong><br>
                                123 Business Street<br>
                                New Delhi, India 110001
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-phone me-2"></i>
                            <div>
                                <strong><?php echo $translations['contact_phone_label']; ?>:</strong><br>
                                +91 9876543210
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-envelope me-2"></i>
                            <div>
                                <strong><?php echo $translations['contact_email_label']; ?>:</strong><br>
                                <EMAIL>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Newsletter Subscription -->
        <div class="row py-4 border-top border-secondary">
            <div class="col-lg-8">
                <h5 class="mb-3">Subscribe to Our Newsletter</h5>
                <p class="mb-3">Stay updated with our latest news and offers.</p>
                <form class="newsletter-form" id="newsletterForm">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter your email address" required>
                        <button class="btn btn-primary" type="submit">Subscribe</button>
                    </div>
                </form>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="language-switcher mt-3">
                    <h6 class="mb-2"><?php echo $translations['switch_language']; ?>:</h6>
                    <button class="btn btn-outline-light btn-sm me-2" onclick="switchLanguage('en')" 
                            <?php echo $lang === 'en' ? 'disabled' : ''; ?>>
                        English
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="switchLanguage('hi')" 
                            <?php echo $lang === 'hi' ? 'disabled' : ''; ?>>
                        हिंदी
                    </button>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div class="row py-3 border-top border-secondary">
            <div class="col-md-6">
                <p class="mb-0">
                    &copy; <?php echo date('Y'); ?> WebsiteDeveloper0002. <?php echo $translations['footer_copyright']; ?>
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <ul class="footer-links-inline">
                    <li><a href="<?php echo BASE_URL; ?>/privacy-policy?lang=<?php echo $lang; ?>">Privacy Policy</a></li>
                    <li><a href="<?php echo BASE_URL; ?>/terms-of-service?lang=<?php echo $lang; ?>">Terms of Service</a></li>
                    <li><a href="<?php echo BASE_URL; ?>/sitemap?lang=<?php echo $lang; ?>">Sitemap</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button class="back-to-top" id="backToTop">
    <i class="fas fa-chevron-up"></i>
</button>

<!-- WhatsApp Float Button -->
<div class="whatsapp-float">
    <a href="https://wa.me/919876543210" target="_blank" class="whatsapp-btn">
        <i class="fab fa-whatsapp"></i>
    </a>
</div>

<!-- Cookie Consent Banner -->
<div class="cookie-consent" id="cookieConsent">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <p class="mb-0">
                    We use cookies to enhance your browsing experience and provide personalized content. 
                    By continuing to use our website, you agree to our 
                    <a href="<?php echo BASE_URL; ?>/privacy-policy?lang=<?php echo $lang; ?>">Privacy Policy</a>.
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <button class="btn btn-primary btn-sm me-2" onclick="acceptCookies()">Accept</button>
                <button class="btn btn-outline-light btn-sm" onclick="declineCookies()">Decline</button>
            </div>
        </div>
    </div>
</div>

<script>
// Newsletter subscription
document.getElementById('newsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const email = this.querySelector('input[type="email"]').value;
    
    // Add your newsletter subscription logic here
    fetch('api/newsletter.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({email: email})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Thank you for subscribing!');
            this.reset();
        } else {
            alert('Subscription failed. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Subscription failed. Please try again.');
    });
});

// Cookie consent
function acceptCookies() {
    localStorage.setItem('cookieConsent', 'accepted');
    document.getElementById('cookieConsent').style.display = 'none';
}

function declineCookies() {
    localStorage.setItem('cookieConsent', 'declined');
    document.getElementById('cookieConsent').style.display = 'none';
}

// Check cookie consent on page load
document.addEventListener('DOMContentLoaded', function() {
    const consent = localStorage.getItem('cookieConsent');
    if (consent) {
        document.getElementById('cookieConsent').style.display = 'none';
    }
});
</script>
