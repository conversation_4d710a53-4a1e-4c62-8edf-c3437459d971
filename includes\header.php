<header class="header">
    <!-- Top Bar -->
    <div class="top-bar d-none d-md-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span><i class="fas fa-phone"></i> +91 9876543210</span>
                        <span><i class="fas fa-envelope"></i> <EMAIL></span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="language-switcher">
                        <button class="btn btn-sm btn-outline-light" onclick="switchLanguage('en')" 
                                <?php echo $lang === 'en' ? 'disabled' : ''; ?>>
                            English
                        </button>
                        <button class="btn btn-sm btn-outline-light" onclick="switchLanguage('hi')" 
                                <?php echo $lang === 'hi' ? 'disabled' : ''; ?>>
                            हिंदी
                        </button>
                    </div>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <img src="assets/images/logo.png" alt="<?php echo $translations['site_title']; ?>" height="50">
            </a>

            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'home' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_home']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'about' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/about?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_about']; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo $page === 'services' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>" 
                           id="servicesDropdown" role="button" data-bs-toggle="dropdown">
                            <?php echo $translations['nav_services']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#web-design">
                                <?php echo $translations['service_web_design']; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#web-development">
                                <?php echo $translations['service_web_development']; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#ecommerce">
                                <?php echo $translations['service_ecommerce']; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/services?lang=<?php echo $lang; ?>#seo">
                                <?php echo $translations['service_seo']; ?>
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'pricing' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/pricing?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_pricing']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'portfolio' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/portfolio?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_portfolio']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'blog' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/blog?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_blog']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'contact' ? 'active' : ''; ?>" 
                           href="<?php echo BASE_URL; ?>/contact?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_contact']; ?>
                        </a>
                    </li>
                </ul>

                <!-- Auth Links -->
                <div class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo $_SESSION['full_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="<?php echo isAdmin() ? 'admin' : 'client'; ?>/dashboard.php">
                                        <i class="fas fa-tachometer-alt"></i> <?php echo $translations['nav_dashboard']; ?>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="auth/logout.php">
                                        <i class="fas fa-sign-out-alt"></i> <?php echo $translations['nav_logout']; ?>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a class="nav-link" href="auth/login.php?lang=<?php echo $lang; ?>">
                            <i class="fas fa-sign-in-alt"></i> <?php echo $translations['nav_login']; ?>
                        </a>
                        <a class="btn btn-primary ms-2" href="auth/register.php?lang=<?php echo $lang; ?>">
                            <?php echo $translations['nav_register']; ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
</header>

<!-- Flash Messages -->
<?php if (hasFlashMessage('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo getFlashMessage('success'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (hasFlashMessage('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?php echo getFlashMessage('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (hasFlashMessage('warning')): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo getFlashMessage('warning'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (hasFlashMessage('info')): ?>
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle"></i> <?php echo getFlashMessage('info'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
