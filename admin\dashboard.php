<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireAdmin();

// Get current language
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
if (!in_array($lang, ['en', 'hi'])) {
    $lang = 'en';
}

// Load language file
$translations = loadLanguage($lang);

// Get dashboard statistics
$stats = getUserStats();

// Get recent activities
try {
    $stmt = $pdo->query("SELECT al.*, u.full_name, u.username 
                        FROM activity_logs al 
                        LEFT JOIN users u ON al.user_id = u.id 
                        ORDER BY al.created_at DESC 
                        LIMIT 10");
    $recentActivities = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentActivities = [];
}

// Get recent messages
$recentMessages = getContactMessages('new');
$recentMessages = array_slice($recentMessages, 0, 5);
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translations['admin_dashboard']; ?> - <?php echo $translations['site_title']; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <?php include 'includes/topnav.php'; ?>
        
        <!-- Dashboard Content -->
        <div class="content-wrapper">
            <div class="container-fluid">
                <!-- Page Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="page-header">
                            <h1 class="page-title">
                                <i class="fas fa-tachometer-alt"></i> <?php echo $translations['admin_dashboard']; ?>
                            </h1>
                            <p class="page-subtitle">
                                <?php echo $translations['dashboard_welcome']; ?>, <?php echo $_SESSION['full_name']; ?>!
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card bg-primary">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo $stats['total_clients']; ?></h3>
                                <p class="stat-label"><?php echo $translations['admin_total_users']; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card bg-success">
                            <div class="stat-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo $stats['total_messages']; ?></h3>
                                <p class="stat-label"><?php echo $translations['admin_total_messages']; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card bg-warning">
                            <div class="stat-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo $stats['new_messages']; ?></h3>
                                <p class="stat-label"><?php echo $translations['admin_new_messages']; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card bg-info">
                            <div class="stat-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo $stats['total_services']; ?></h3>
                                <p class="stat-label"><?php echo $translations['admin_total_services']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-bolt"></i> <?php echo $translations['dashboard_quick_actions']; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="users.php" class="quick-action-btn">
                                            <i class="fas fa-users"></i>
                                            <span><?php echo $translations['admin_users']; ?></span>
                                        </a>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="pages.php" class="quick-action-btn">
                                            <i class="fas fa-file-alt"></i>
                                            <span><?php echo $translations['admin_pages']; ?></span>
                                        </a>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="services.php" class="quick-action-btn">
                                            <i class="fas fa-cogs"></i>
                                            <span><?php echo $translations['admin_services']; ?></span>
                                        </a>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="messages.php" class="quick-action-btn">
                                            <i class="fas fa-envelope"></i>
                                            <span><?php echo $translations['admin_messages']; ?></span>
                                        </a>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="settings.php" class="quick-action-btn">
                                            <i class="fas fa-cog"></i>
                                            <span><?php echo $translations['admin_settings']; ?></span>
                                        </a>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                                        <a href="../?lang=<?php echo $lang; ?>" class="quick-action-btn" target="_blank">
                                            <i class="fas fa-external-link-alt"></i>
                                            <span>View Site</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity & Messages -->
                <div class="row">
                    <!-- Recent Activity -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-history"></i> <?php echo $translations['dashboard_recent_activity']; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentActivities)): ?>
                                    <div class="activity-list">
                                        <?php foreach ($recentActivities as $activity): ?>
                                            <div class="activity-item">
                                                <div class="activity-icon">
                                                    <i class="fas fa-circle"></i>
                                                </div>
                                                <div class="activity-content">
                                                    <p class="activity-text">
                                                        <strong><?php echo htmlspecialchars($activity['full_name'] ?? $activity['username'] ?? 'Unknown'); ?></strong>
                                                        <?php echo htmlspecialchars($activity['action']); ?>
                                                        <?php if ($activity['details']): ?>
                                                            - <?php echo htmlspecialchars($activity['details']); ?>
                                                        <?php endif; ?>
                                                    </p>
                                                    <small class="activity-time text-muted">
                                                        <i class="fas fa-clock"></i> 
                                                        <?php echo formatDate($activity['created_at'], 'M j, Y g:i A'); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center py-4">
                                        <i class="fas fa-info-circle"></i> No recent activity found.
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Messages -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-envelope"></i> Recent Messages
                                </h5>
                                <a href="messages.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentMessages)): ?>
                                    <div class="message-list">
                                        <?php foreach ($recentMessages as $message): ?>
                                            <div class="message-item">
                                                <div class="message-header">
                                                    <strong><?php echo htmlspecialchars($message['name']); ?></strong>
                                                    <small class="text-muted">
                                                        <?php echo formatDate($message['created_at'], 'M j'); ?>
                                                    </small>
                                                </div>
                                                <p class="message-subject">
                                                    <?php echo htmlspecialchars($message['subject'] ?? 'No Subject'); ?>
                                                </p>
                                                <p class="message-preview">
                                                    <?php echo substr(htmlspecialchars($message['message']), 0, 100) . '...'; ?>
                                                </p>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center py-4">
                                        <i class="fas fa-inbox"></i> No new messages.
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Information -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-info-circle"></i> System Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>PHP Version:</strong><br>
                                        <?php echo PHP_VERSION; ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Server Software:</strong><br>
                                        <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Database:</strong><br>
                                        MySQL <?php echo $pdo->getAttribute(PDO::ATTR_SERVER_VERSION); ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Current Time:</strong><br>
                                        <?php echo date('Y-m-d H:i:s'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    
    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
        
        // Initialize tooltips
        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
</body>
</html>
