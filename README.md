# WebsiteDeveloper0002 - Professional Business Website

A complete business website built with HTML, CSS, JavaScript, PHP, and MySQL featuring bilingual support (English/Hindi), admin dashboard, client dashboard, and comprehensive business features.

## 🚀 Features

### Core Features
- **Bilingual Support**: English and Hindi with SEO-friendly URLs
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Clean URLs**: SEO-friendly routing with .htaccess
- **Authentication System**: Secure login/registration with role-based access
- **Admin Dashboard**: Complete content management system
- **Client Dashboard**: User-specific project and account management
- **Contact System**: Advanced contact forms with email notifications
- **SEO Optimized**: Meta tags, structured data, and search engine friendly

### Landing Pages
- **Home Page**: Hero section, services overview, testimonials, stats
- **About Page**: Company information, team, values, statistics
- **Services Page**: Detailed service listings with process workflow
- **Pricing Page**: Flexible pricing plans with add-on services
- **Contact Page**: Contact form, business information, FAQ, map
- **Portfolio Page**: Project showcase (ready for content)
- **Blog Page**: News and articles (ready for content)

### Admin Features
- User management (view, edit, delete users)
- Content management (pages, services, messages)
- Contact message management with status tracking
- Dashboard with statistics and quick actions
- Activity logging and monitoring
- Newsletter subscriber management

### Client Features
- Personal dashboard with project overview
- Message history and status tracking
- Profile management
- Project status monitoring
- Invoice and billing information
- Support ticket system

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Poppins)
- **Server**: Apache with mod_rewrite

## 📁 Project Structure

```
websitedeveloper0002.in/
├── admin/                  # Admin dashboard
│   ├── dashboard.php
│   ├── users.php
│   ├── messages.php
│   └── includes/
├── api/                    # API endpoints
│   ├── contact.php
│   └── newsletter.php
├── assets/                 # Static assets
│   ├── css/
│   ├── js/
│   └── images/
├── auth/                   # Authentication
│   ├── login.php
│   ├── register.php
│   └── logout.php
├── client/                 # Client dashboard
│   └── dashboard.php
├── config/                 # Configuration
│   └── database.php
├── includes/               # Shared includes
│   ├── header.php
│   ├── footer.php
│   └── functions.php
├── languages/              # Translations
│   ├── en.php
│   └── hi.php
├── pages/                  # Page content
│   ├── home.php
│   ├── about.php
│   ├── services.php
│   ├── pricing.php
│   └── contact.php
├── .htaccess              # URL rewriting
└── index.php              # Main entry point
```

## 🚀 Installation

### Prerequisites
- Apache web server with mod_rewrite enabled
- PHP 7.4 or higher
- MySQL 5.7 or higher
- SMTP server for email functionality (optional)

### Setup Steps

1. **Clone/Download the project**
   ```bash
   git clone [repository-url]
   cd websitedeveloper0002.in
   ```

2. **Configure Database**
   - Update database credentials in `config/database.php`
   - The database and tables will be created automatically on first run

3. **Configure Web Server**
   - Ensure mod_rewrite is enabled
   - Set document root to the project directory
   - Ensure .htaccess files are processed

4. **Set Permissions**
   ```bash
   chmod 755 uploads/
   chmod 644 .htaccess
   ```

5. **Configure Email (Optional)**
   - Update SMTP settings in `config/database.php`
   - Or use PHP's mail() function with proper server configuration

## 🔧 Configuration

### Database Configuration
Edit `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'websitedeveloper0002');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### Site Configuration
Update constants in `config/database.php`:
```php
define('SITE_NAME', 'WebsiteDeveloper0002');
define('BASE_URL', 'https://websitedeveloper0002.in');
define('ADMIN_EMAIL', '<EMAIL>');
```

## 🌐 URL Structure

### Public Pages
- `/` - Home page
- `/about` - About page
- `/services` - Services page
- `/pricing` - Pricing page
- `/contact` - Contact page
- `/portfolio` - Portfolio page
- `/blog` - Blog page

### Language Support
- `/en/about` - English about page
- `/hi/about` - Hindi about page
- `?lang=hi` - Switch to Hindi
- `?lang=en` - Switch to English

### Authentication
- `/auth/login` - Login page
- `/auth/register` - Registration page
- `/auth/logout` - Logout

### Dashboards
- `/admin/dashboard.php` - Admin dashboard
- `/client/dashboard.php` - Client dashboard

## 👤 Default Admin Account

After installation, create an admin account by registering and then updating the database:
```sql
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

## 🎨 Customization

### Adding New Pages
1. Create page content in `pages/your-page.php`
2. Add URL routing in `.htaccess`
3. Add navigation links in `includes/header.php`
4. Add translations in `languages/en.php` and `languages/hi.php`

### Styling
- Main styles: `assets/css/style.css`
- Responsive styles: `assets/css/responsive.css`
- Admin styles: `assets/css/admin.css`
- Client styles: `assets/css/client.css`

### Adding Languages
1. Create new language file in `languages/`
2. Update language detection in `index.php`
3. Add language switcher options

## 🔒 Security Features

- CSRF protection on all forms
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- Password hashing with PHP's password_hash()
- Session security with secure headers
- Rate limiting on contact forms
- Input validation and sanitization

## 📱 Responsive Design

- Mobile-first approach
- Bootstrap 5 grid system
- Custom responsive breakpoints
- Touch-friendly navigation
- Optimized images and assets

## 🔍 SEO Features

- Clean, semantic HTML structure
- Meta tags for all pages
- Open Graph tags for social sharing
- Structured data markup
- XML sitemap generation
- Multilingual SEO support
- Fast loading times
- Mobile optimization

## 🧪 Testing

### Manual Testing Checklist
- [ ] All pages load correctly
- [ ] Language switching works
- [ ] Contact form submissions
- [ ] User registration and login
- [ ] Admin dashboard functionality
- [ ] Client dashboard features
- [ ] Responsive design on mobile
- [ ] Cross-browser compatibility

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers

## 📞 Support

For support and customization requests:
- Email: <EMAIL>
- Phone: +91 **********
- Website: https://websitedeveloper0002.in

## 📄 License

This project is proprietary software. All rights reserved.

## 🔄 Updates

### Version 1.0.0 (Current)
- Initial release with full feature set
- Bilingual support (English/Hindi)
- Admin and client dashboards
- Complete authentication system
- Responsive design
- SEO optimization

---

**Built with ❤️ by WebsiteDeveloper0002 Team**
