RewriteEngine On

# Force HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slash
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [R=301,L]

# Handle language parameter
RewriteCond %{QUERY_STRING} ^lang=([a-z]{2})$
RewriteRule ^$ index.php?lang=%1 [L]

# Clean URL routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Home page
RewriteRule ^$ index.php [L]

# Pages with language support
RewriteRule ^(about|services|pricing|portfolio|blog|contact)/?$ index.php?page=$1 [L,QSA]
RewriteRule ^(about|services|pricing|portfolio|blog|contact)/([a-z]{2})/?$ index.php?page=$1&lang=$2 [L,QSA]

# Language first routing
RewriteRule ^(en|hi)/?$ index.php?lang=$1 [L,QSA]
RewriteRule ^(en|hi)/(about|services|pricing|portfolio|blog|contact)/?$ index.php?lang=$1&page=$2 [L,QSA]

# Blog post routing
RewriteRule ^blog/([^/]+)/?$ index.php?page=blog&post=$1 [L,QSA]
RewriteRule ^(en|hi)/blog/([^/]+)/?$ index.php?page=blog&post=$2&lang=$1 [L,QSA]

# Service category routing
RewriteRule ^services/([^/]+)/?$ index.php?page=services&category=$1 [L,QSA]
RewriteRule ^(en|hi)/services/([^/]+)/?$ index.php?page=services&category=$2&lang=$1 [L,QSA]

# Portfolio category routing
RewriteRule ^portfolio/([^/]+)/?$ index.php?page=portfolio&category=$1 [L,QSA]
RewriteRule ^(en|hi)/portfolio/([^/]+)/?$ index.php?page=portfolio&category=$2&lang=$1 [L,QSA]

# API routes
RewriteRule ^api/([^/]+)/?$ api/$1.php [L,QSA]

# Admin routes (protect admin directory)
RewriteRule ^admin/(.*)$ admin/$1 [L,QSA]

# Auth routes
RewriteRule ^auth/([^/]+)/?$ auth/$1.php [L,QSA]

# Client routes
RewriteRule ^client/(.*)$ client/$1 [L,QSA]

# Static pages
RewriteRule ^(privacy-policy|terms-of-service|sitemap)/?$ pages/static.php?page=$1 [L,QSA]
RewriteRule ^(en|hi)/(privacy-policy|terms-of-service|sitemap)/?$ pages/static.php?page=$2&lang=$1 [L,QSA]

# Handle 404 errors
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . index.php?page=404 [L,QSA]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
RedirectMatch 404 /config/
RedirectMatch 404 /includes/

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType video/mp4 "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?websitedeveloper0002\.in [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?localhost [NC]
RewriteRule \.(jpg|jpeg|png|gif|svg)$ - [NC,F,L]

# Custom error pages
ErrorDocument 404 /index.php?page=404
ErrorDocument 403 /index.php?page=403
ErrorDocument 500 /index.php?page=500
