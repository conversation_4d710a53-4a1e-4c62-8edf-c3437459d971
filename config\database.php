<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'websitedeveloper0002_db');

// Site configuration
define('BASE_URL', 'http://localhost/websitedeveloper0002.in');
define('SITE_NAME', 'WebsiteDeveloper0002');
define('ADMIN_EMAIL', '<EMAIL>');

// Create database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Create database and tables if they don't exist
function createDatabase() {
    try {
        // Connect without database name first
        $pdo_temp = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Now connect to the database
        global $pdo;
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create tables
        createTables();
        
    } catch(PDOException $e) {
        die("Database creation failed: " . $e->getMessage());
    }
}

function createTables() {
    global $pdo;
    
    // Users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('admin', 'client') DEFAULT 'client',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Pages table for multilingual content
    $pdo->exec("CREATE TABLE IF NOT EXISTS pages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_key VARCHAR(50) NOT NULL,
        language VARCHAR(2) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        keywords TEXT,
        content LONGTEXT,
        meta_title VARCHAR(255),
        meta_description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_page_lang (page_key, language)
    )");
    
    // Settings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        language VARCHAR(2) DEFAULT 'en',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Contact messages table
    $pdo->exec("CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        subject VARCHAR(255),
        message TEXT NOT NULL,
        budget VARCHAR(100),
        timeline VARCHAR(100),
        newsletter TINYINT(1) DEFAULT 0,
        ip_address VARCHAR(45),
        user_agent TEXT,
        status ENUM('new', 'read', 'replied') DEFAULT 'new',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Newsletter subscribers table
    $pdo->exec("CREATE TABLE IF NOT EXISTS newsletter_subscribers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(100) UNIQUE NOT NULL,
        name VARCHAR(100),
        status ENUM('active', 'inactive', 'unsubscribed') DEFAULT 'active',
        subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        unsubscribed_at TIMESTAMP NULL
    )");

    // Activity logs table
    $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )");

    // User projects table (placeholder for future use)
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        budget DECIMAL(10,2),
        start_date DATE,
        end_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    // Site settings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS site_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Services table
    $pdo->exec("CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title_en VARCHAR(255) NOT NULL,
        title_hi VARCHAR(255) NOT NULL,
        description_en TEXT,
        description_hi TEXT,
        icon VARCHAR(100),
        price DECIMAL(10,2),
        features_en TEXT,
        features_hi TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insert default admin user
    $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
    if ($adminExists == 0) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT INTO users (username, email, password, full_name, role) VALUES 
                   ('admin', '<EMAIL>', '$hashedPassword', 'Administrator', 'admin')");
    }
    
    // Insert default page content
    insertDefaultContent();
}

function insertDefaultContent() {
    global $pdo;
    
    // Check if content already exists
    $contentExists = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
    if ($contentExists > 0) return;
    
    // Default pages content
    $defaultPages = [
        [
            'page_key' => 'home',
            'language' => 'en',
            'title' => 'Professional Web Development Services',
            'description' => 'Expert web development services for businesses. Custom websites, e-commerce solutions, and digital marketing.',
            'keywords' => 'web development, website design, e-commerce, digital marketing',
            'content' => 'Welcome to our professional web development services.'
        ],
        [
            'page_key' => 'home',
            'language' => 'hi',
            'title' => 'पेशेवर वेब डेवलपमेंट सेवाएं',
            'description' => 'व्यवसायों के लिए विशेषज्ञ वेब डेवलपमेंट सेवाएं। कस्टम वेबसाइट, ई-कॉमर्स समाधान, और डिजिटल मार्केटिंग।',
            'keywords' => 'वेब डेवलपमेंट, वेबसाइट डिज़ाइन, ई-कॉमर्स, डिजिटल मार्केटिंग',
            'content' => 'हमारी पेशेवर वेब डेवलपमेंट सेवाओं में आपका स्वागत है।'
        ]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO pages (page_key, language, title, description, keywords, content) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($defaultPages as $page) {
        $stmt->execute([$page['page_key'], $page['language'], $page['title'], $page['description'], $page['keywords'], $page['content']]);
    }
}

// Initialize database if needed
if (!file_exists('install.lock')) {
    createDatabase();
    file_put_contents('install.lock', 'installed');
}
?>
